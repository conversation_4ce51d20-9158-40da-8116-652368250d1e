# YgInvoiceCheckContext 重构说明

## 概述

本次重构将 `YgInvoiceCheckContext` 从构造函数模式改为Builder模式，并添加了应付单类型枚举，支持逐步构建Context，特别适合需要分阶段处理的场景。

## 主要变更

### 1. 新增应付单类型枚举

**文件**: `PayableType.java`

```java
public enum PayableType {
    PURCHASE("F1-Cxx-02", "采购应付"),
    PROJECT("F1-Cxx-01", "项目应付");
}
```

### 2. Context类重构

**主要变更**:
- 添加了 `PayableType payableType` 字段
- 改为私有构造函数 + Builder模式
- 添加了便利方法：`isPurchasePayable()`, `isProjectPayable()`, `getPayableTypeName()`
- 支持 `continueWith()` 方法进行增量构建
- 添加状态标记，避免重复执行相同的构建步骤

### 3. 增量构建支持

**基础构建**:
```java
YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
    .withSourceData(source, sourceDetails)
    .build();
```

**继续构建**:
```java
context = context.continueWith()
    .withConvertData(convert, convertDetails)
    .withBasicInfo()
    .build();
```

## 单据类型说明

### 采购应付 (PURCHASE)
- **上游单据**: 采购订单
- **源头单据**: 采购订单（与上游相同）
- **补充信息**: 入库单

### 项目应付 (PROJECT)
- **上游单据**: 收票登记
- **源头单据**: 付款合同或费用结算单

## FiSourcePuPayableServiceImpl中的分阶段处理

### 三个阶段的异常处理

1. **订单同步失败** (`PushStatusEnum.ORDER_SYNC_FAIL`)
2. **应付单转换失败** (`PushStatusEnum.CONVERT_FAIL`)
3. **应付单推送失败** (`PushStatusEnum.PUSH_FAIL`)

### 统一异常处理

```java
try {
    // 第一阶段：订单同步
    // 第二阶段：应付单转换
    // 第三阶段：应付单推送
} catch (Exception e) {
    handleUnifiedException(source, e);
    throw new CheckedException("处理失败:" + e.getMessage());
}
```

## 使用示例

### 示例1：FiSourcePuPayableServiceImpl中的分阶段构建
```java
// 第一阶段：订单同步（仅项目应付需要）
YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
    .withSourceData(source, sourceDetails)
    .build();

if (context.isProjectPayable()) {
    context = context.continueWith()
        .withBasicInfo()        // 基础信息
        .withTopBill()          // 上游单据信息（收票登记）
        .withSourceBill()       // 源头单据信息（付款合同或费用结算单）
        .build();
    
    // 使用context进行订单同步
    processBaseOrderSync(context);
}

// 第二阶段：转换数据后更新context
convertData(source, sourceDetails);
FiConvertPuPayable convert = getConvertData(source.getPkPayablebill());
List<FiConvertPuPayableDetail> convertDetails = getConvertDetails(source.getPkPayablebill());

context = context.continueWith()
    .withConvertData(convert, convertDetails)
    .build();

// 第三阶段：构建完整context用于推送
context = context.continueWith()
    .withCosTypeInfo()      // 费用类型信息
    .withInvoiceInfo()      // 发票信息
    .withAccountInfo()      // 账户信息
    .withImageInfo()        // 影像信息
    .withValidation()       // 业务校验
    .build();

// 使用完整context推送
pushToYgPlatform(source, context);
```

### 示例2：最小化构建（仅获取上游订单数据）
```java
YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
    .withConvertData(convert, convertDetails)
    .withBasicInfo()    // 基础信息
    .withTopBill()      // 上游单据信息
    .build();

// 获取数据
if (context.isPurchasePayable()) {
    Map<String, PoOrderB> orders = context.getPoOrderBMap();
} else if (context.isProjectPayable()) {
    Map<String, PmContr> contracts = context.getPmContrMap();
}
```

## Builder方法说明

| 方法 | 说明 | 幂等性 |
|------|------|--------|
| `withSourceData()` | 设置源数据（可选） | ✓ |
| `withConvertData()` | 设置转换数据（必需） | ✓ |
| `withBasicInfo()` | 准备基础信息 | ✓ |
| `withCosTypeInfo()` | 准备费用类型信息 | ✓ |
| `withInvoiceInfo()` | 准备发票信息 | ✓ |
| `withAccountInfo()` | 准备账户信息 | ✓ |
| `withTopBill()` | 准备上游单据信息 | ✓ |
| `withSourceBill()` | 准备源头单据信息 | ✓ |
| `withImageInfo()` | 准备影像信息 | ✓ |
| `withValidation()` | 执行业务校验 | ✓ |
| `continueWith()` | 基于现有Context继续构建 | - |

## 主要优势

1. **分阶段构建**: 支持按需构建，适合复杂的业务流程
2. **增量构建**: `continueWith()` 方法支持在现有Context基础上继续添加数据
3. **幂等性**: 所有构建方法都是幂等的，重复调用不会产生副作用
4. **统一异常处理**: 简化了异常处理逻辑
5. **类型安全**: 使用枚举替代硬编码字符串
6. **状态管理**: 内部状态标记避免重复执行

## 注意事项

1. `withTopBill()` 和 `withSourceBill()` 的行为取决于应付单类型
2. 使用 `continueWith()` 时，原有的Context状态会被保留
3. 建议在service层使用新的Builder模式，逐步替换旧的构造函数调用
4. 异常处理已统一，不需要在每个阶段单独处理异常
